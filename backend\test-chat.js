// Script de test pour vérifier les routes de chat
const express = require('express');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();
app.use(express.json());

// Test d'importation des modèles
try {
  console.log('🔍 Test d\'importation des modèles...');
  const { Conversation, Message } = require('./models/Chat');
  console.log('✅ Modèles Chat importés avec succès');
  
  const User = require('./models/User');
  console.log('✅ Modèle User importé avec succès');
} catch (error) {
  console.error('❌ Erreur d\'importation des modèles:', error.message);
  process.exit(1);
}

// Test d'importation du contrôleur
try {
  console.log('🔍 Test d\'importation du contrôleur...');
  const chatController = require('./controllers/chatController');
  console.log('✅ Contrôleur Chat importé avec succès');
} catch (error) {
  console.error('❌ Erreur d\'importation du contrôleur:', error.message);
  process.exit(1);
}

// Test d'importation des routes
try {
  console.log('🔍 Test d\'importation des routes...');
  const chatRoutes = require('./routes/chatRoutes');
  console.log('✅ Routes Chat importées avec succès');
} catch (error) {
  console.error('❌ Erreur d\'importation des routes:', error.message);
  process.exit(1);
}

// Test de connexion MongoDB
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => {
    console.log('✅ MongoDB connecté avec succès');
    
    // Configurer les routes de test
    const chatRoutes = require('./routes/chatRoutes');
    app.use('/api/chat', chatRoutes);
    
    // Route de test simple
    app.get('/test', (req, res) => {
      res.json({ message: 'Test réussi', timestamp: new Date().toISOString() });
    });
    
    const PORT = 3001; // Port différent pour éviter les conflits
    app.listen(PORT, () => {
      console.log(`🚀 Serveur de test démarré sur le port ${PORT}`);
      console.log(`📡 Testez: http://localhost:${PORT}/test`);
      console.log('🔧 Routes de chat disponibles sur /api/chat/*');
    });
  })
  .catch(err => {
    console.error('❌ Erreur de connexion MongoDB:', err.message);
    process.exit(1);
  });
