const { Conversation, Message } = require('../models/Chat');
const User = require('../models/User');
const mongoose = require('mongoose');

// Récupérer toutes les conversations d'un utilisateur
const getUserConversations = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const conversations = await Conversation.find({
      participants: userId,
      isActive: true
    })
    .populate({
      path: 'participants',
      select: 'nom prenom role photoUrl',
      match: { _id: { $ne: userId } }
    })
    .populate({
      path: 'lastMessage',
      select: 'content createdAt senderId messageType'
    })
    .sort({ lastMessageTime: -1 });

    // Filtrer les conversations qui ont des participants valides
    const validConversations = conversations.filter(conv => 
      conv.participants && conv.participants.length > 0
    );

    res.json(validConversations);
  } catch (error) {
    console.error('Erreur lors de la récupération des conversations:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Récupérer les messages d'une conversation
const getConversationMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;

    // Vérifier que l'utilisateur fait partie de la conversation
    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: userId
    });

    if (!conversation) {
      return res.status(404).json({ message: 'Conversation non trouvée' });
    }

    const messages = await Message.find({ conversationId })
      .populate('senderId', 'nom prenom photoUrl')
      .populate('receiverId', 'nom prenom photoUrl')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Marquer les messages comme lus
    await Message.updateMany(
      { 
        conversationId, 
        receiverId: userId, 
        isRead: false 
      },
      { 
        isRead: true, 
        readAt: new Date() 
      }
    );

    res.json({
      messages: messages.reverse(), // Inverser pour avoir l'ordre chronologique
      pagination: {
        page,
        limit,
        hasMore: messages.length === limit
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des messages:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Envoyer un message
const sendMessage = async (req, res) => {
  try {
    const { receiverId, content, messageType = 'text' } = req.body;
    const senderId = req.user.id;

    if (!receiverId || !content) {
      return res.status(400).json({ message: 'Destinataire et contenu requis' });
    }

    // Vérifier que le destinataire existe
    const receiver = await User.findById(receiverId);
    if (!receiver) {
      return res.status(404).json({ message: 'Destinataire non trouvé' });
    }

    // Chercher ou créer une conversation
    let conversation = await Conversation.findOne({
      participants: { $all: [senderId, receiverId] },
      isActive: true
    });

    if (!conversation) {
      conversation = new Conversation({
        participants: [senderId, receiverId]
      });
      await conversation.save();
    }

    // Créer le message
    const message = new Message({
      conversationId: conversation._id,
      senderId,
      receiverId,
      content,
      messageType
    });

    await message.save();

    // Populer les données pour la réponse
    await message.populate('senderId', 'nom prenom photoUrl');
    await message.populate('receiverId', 'nom prenom photoUrl');

    res.status(201).json(message);
  } catch (error) {
    console.error('Erreur lors de l\'envoi du message:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Rechercher des utilisateurs pour démarrer une conversation
const searchUsers = async (req, res) => {
  try {
    const { query, role } = req.query;
    const currentUserId = req.user.id;

    if (!query || query.length < 2) {
      return res.status(400).json({ message: 'Requête de recherche trop courte' });
    }

    const searchCriteria = {
      _id: { $ne: currentUserId },
      $or: [
        { nom: { $regex: query, $options: 'i' } },
        { prenom: { $regex: query, $options: 'i' } }
      ]
    };

    if (role && ['client', 'conducteur'].includes(role)) {
      searchCriteria.role = role;
    }

    const users = await User.find(searchCriteria)
      .select('nom prenom role photoUrl ville')
      .limit(20);

    res.json(users);
  } catch (error) {
    console.error('Erreur lors de la recherche d\'utilisateurs:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Marquer une conversation comme lue
const markConversationAsRead = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    await Message.updateMany(
      { 
        conversationId, 
        receiverId: userId, 
        isRead: false 
      },
      { 
        isRead: true, 
        readAt: new Date() 
      }
    );

    res.json({ message: 'Messages marqués comme lus' });
  } catch (error) {
    console.error('Erreur lors du marquage comme lu:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

// Obtenir le nombre de messages non lus
const getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;

    const unreadCount = await Message.countDocuments({
      receiverId: userId,
      isRead: false
    });

    res.json({ unreadCount });
  } catch (error) {
    console.error('Erreur lors du comptage des messages non lus:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
};

module.exports = {
  getUserConversations,
  getConversationMessages,
  sendMessage,
  searchUsers,
  markConversationAsRead,
  getUnreadCount
};
