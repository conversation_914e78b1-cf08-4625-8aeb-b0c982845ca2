const mongoose = require('mongoose');

// Modèle pour les conversations entre utilisateurs
const ConversationSchema = new mongoose.Schema({
  participants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }],
  lastMessage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  },
  lastMessageTime: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, { timestamps: true });

// Index pour optimiser les recherches de conversations
ConversationSchema.index({ participants: 1 });
ConversationSchema.index({ lastMessageTime: -1 });

// Modèle pour les messages individuels
const MessageSchema = new mongoose.Schema({
  conversationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true
  },
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  receiverId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 1000
  },
  messageType: {
    type: String,
    enum: ['text', 'image', 'location'],
    default: 'text'
  },
  isRead: {
    type: Boolean,
    default: false
  },
  readAt: {
    type: Date
  }
}, { timestamps: true });

// Index pour optimiser les recherches de messages
MessageSchema.index({ conversationId: 1, createdAt: -1 });
MessageSchema.index({ senderId: 1 });
MessageSchema.index({ receiverId: 1, isRead: 1 });

// Middleware pour mettre à jour la conversation après l'ajout d'un message
MessageSchema.post('save', async function() {
  try {
    await mongoose.model('Conversation').findByIdAndUpdate(
      this.conversationId,
      {
        lastMessage: this._id,
        lastMessageTime: this.createdAt
      }
    );
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la conversation:', error);
  }
});

const Conversation = mongoose.model('Conversation', ConversationSchema);
const Message = mongoose.model('Message', MessageSchema);

module.exports = { Conversation, Message };