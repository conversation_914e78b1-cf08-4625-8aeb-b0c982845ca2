const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Middleware pour ngrok
app.use((req, res, next) => {
  res.setHeader('ngrok-skip-browser-warning', 'true');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, ngrok-skip-browser-warning, User-Agent, Accept');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  next();
});

console.log('🔍 Démarrage du serveur minimal...');

// Test de connexion MongoDB
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log("✅ MongoDB connecté"))
  .catch(err => console.log("❌ Erreur MongoDB:", err));

// Test d'importation des modèles
let Conversation, Message, User;
try {
  console.log('🔍 Test d\'importation des modèles...');
  const chatModels = require('./models/Chat');
  Conversation = chatModels.Conversation;
  Message = chatModels.Message;
  User = require('./models/User');
  console.log('✅ Modèles importés avec succès');
} catch (error) {
  console.error('❌ Erreur d\'importation des modèles:', error.message);
  process.exit(1);
}

// Middleware d'authentification simple
const authMiddleware = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ message: 'Authentification requise' });
  }
  try {
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = { id: decoded.userId, role: decoded.role };
    next();
  } catch (error) {
    res.status(401).json({ message: 'Token invalide' });
  }
};

// Routes de test simples
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Serveur minimal fonctionnel',
    timestamp: new Date().toISOString()
  });
});

// Route de test pour les conversations
app.get('/api/chat/conversations', authMiddleware, async (req, res) => {
  try {
    console.log('📞 Requête conversations pour utilisateur:', req.user.id);
    
    const conversations = await Conversation.find({
      participants: req.user.id,
      isActive: true
    })
    .populate({
      path: 'participants',
      select: 'nom prenom role photoUrl',
      match: { _id: { $ne: req.user.id } }
    })
    .populate({
      path: 'lastMessage',
      select: 'content createdAt senderId messageType'
    })
    .sort({ lastMessageTime: -1 });

    console.log('✅ Conversations trouvées:', conversations.length);
    res.json(conversations);
  } catch (error) {
    console.error('❌ Erreur conversations:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Route de test pour la recherche d'utilisateurs
app.get('/api/chat/users/search', authMiddleware, async (req, res) => {
  try {
    const { query, role } = req.query;
    console.log('🔍 Recherche utilisateurs:', { query, role, currentUser: req.user.id });

    if (!query || query.length < 2) {
      return res.status(400).json({ message: 'Requête de recherche trop courte' });
    }

    const searchCriteria = {
      _id: { $ne: req.user.id },
      $or: [
        { nom: { $regex: query, $options: 'i' } },
        { prenom: { $regex: query, $options: 'i' } }
      ]
    };

    if (role && ['client', 'conducteur'].includes(role)) {
      searchCriteria.role = role;
    }

    const users = await User.find(searchCriteria)
      .select('nom prenom role photoUrl ville')
      .limit(20);

    console.log('✅ Utilisateurs trouvés:', users.length);
    res.json(users);
  } catch (error) {
    console.error('❌ Erreur recherche:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Route de test pour envoyer un message
app.post('/api/chat/messages', authMiddleware, async (req, res) => {
  try {
    const { receiverId, content, messageType = 'text' } = req.body;
    const senderId = req.user.id;

    console.log('📨 Envoi message:', { senderId, receiverId, content: content.substring(0, 50) + '...' });

    if (!receiverId || !content) {
      return res.status(400).json({ message: 'Destinataire et contenu requis' });
    }

    // Vérifier que le destinataire existe
    const receiver = await User.findById(receiverId);
    if (!receiver) {
      return res.status(404).json({ message: 'Destinataire non trouvé' });
    }

    // Chercher ou créer une conversation
    let conversation = await Conversation.findOne({
      participants: { $all: [senderId, receiverId] },
      isActive: true
    });

    if (!conversation) {
      conversation = new Conversation({
        participants: [senderId, receiverId]
      });
      await conversation.save();
      console.log('✅ Nouvelle conversation créée:', conversation._id);
    }

    // Créer le message
    const message = new Message({
      conversationId: conversation._id,
      senderId,
      receiverId,
      content,
      messageType
    });

    await message.save();

    // Populer les données pour la réponse
    await message.populate('senderId', 'nom prenom photoUrl');
    await message.populate('receiverId', 'nom prenom photoUrl');

    console.log('✅ Message envoyé:', message._id);
    res.status(201).json(message);
  } catch (error) {
    console.error('❌ Erreur envoi message:', error);
    res.status(500).json({ message: 'Erreur serveur', error: error.message });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err.stack);
  res.status(500).json({ message: 'Erreur serveur inattendue', error: err.message });
});

// Route 404
app.use('*', (req, res) => {
  console.log(`❌ Route non trouvée: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ message: 'Route non trouvée', path: req.originalUrl });
});

const PORT = 5001;
app.listen(PORT, () => {
  console.log(`🚀 Serveur minimal démarré sur le port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/api/health`);
  console.log(`💬 Chat API: http://localhost:${PORT}/api/chat/*`);
});
