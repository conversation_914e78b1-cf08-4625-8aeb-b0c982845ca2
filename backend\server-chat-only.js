const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Middleware pour ngrok
app.use((req, res, next) => {
  res.setHeader('ngrok-skip-browser-warning', 'true');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, ngrok-skip-browser-warning, User-Agent, Accept');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  next();
});

// Connexion MongoDB
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log("✅ MongoDB connecté"))
  .catch(err => console.log("❌ Erreur MongoDB:", err));

// Routes de chat simplifiées pour éviter les erreurs path-to-regexp
const authMiddleware = require('./middlewares/authMiddleware');
const {
  getUserConversations,
  getConversationMessages,
  sendMessage,
  searchUsers,
  markConversationAsRead,
  getUnreadCount
} = require('./controllers/chatController');

// Routes de chat directes
app.get('/api/chat/conversations', authMiddleware, getUserConversations);
app.get('/api/chat/conversations/:conversationId/messages', authMiddleware, getConversationMessages);
app.put('/api/chat/conversations/:conversationId/read', authMiddleware, markConversationAsRead);
app.post('/api/chat/messages', authMiddleware, sendMessage);
app.get('/api/chat/messages/unread-count', authMiddleware, getUnreadCount);
app.get('/api/chat/users/search', authMiddleware, searchUsers);

// Route de test
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Serveur chat fonctionnel',
    timestamp: new Date().toISOString(),
    endpoints: {
      conversations: '/api/chat/conversations',
      messages: '/api/chat/messages',
      search: '/api/chat/users/search'
    }
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err.stack);
  res.status(500).json({ message: 'Erreur serveur inattendue', error: err.message });
});

// Route 404
app.use('*', (req, res) => {
  console.log(`❌ Route non trouvée: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ message: 'Route non trouvée', path: req.originalUrl });
});

const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
  console.log(`🚀 Serveur chat démarré sur le port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/api/health`);
});
