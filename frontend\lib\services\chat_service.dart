import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import 'auth_service.dart';

class ChatService {
  // URL temporaire pour les tests de chat - changez ceci selon votre configuration
  // static const String baseUrl = '${ApiConfig.baseUrl}/chat';
  // Pour tester avec le serveur de chat local, décommentez la ligne suivante :
  static const String baseUrl = 'http://localhost:5001/api/chat';

  // Modèles de données
  static Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'true',
    'User-Agent': 'Flutter App',
    'Accept': 'application/json',
  };

  static Future<Map<String, String>> _getAuthHeaders() async {
    final token = await AuthService.getToken();
    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  // Récupérer les conversations de l'utilisateur
  static Future<List<Conversation>> getUserConversations() async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('$baseUrl/conversations'),
        headers: headers,
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Conversation.fromJson(json)).toList();
      } else {
        throw Exception('Erreur lors de la récupération des conversations: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur ChatService.getUserConversations: $e');
      throw Exception('Erreur de connexion: $e');
    }
  }

  // Récupérer les messages d'une conversation
  static Future<MessagesResponse> getConversationMessages(String conversationId, {int page = 1, int limit = 50}) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('$baseUrl/conversations/$conversationId/messages?page=$page&limit=$limit'),
        headers: headers,
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return MessagesResponse.fromJson(data);
      } else {
        throw Exception('Erreur lors de la récupération des messages: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur ChatService.getConversationMessages: $e');
      throw Exception('Erreur de connexion: $e');
    }
  }

  // Envoyer un message
  static Future<Message> sendMessage(String receiverId, String content, {String messageType = 'text'}) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.post(
        Uri.parse('$baseUrl/messages'),
        headers: headers,
        body: json.encode({
          'receiverId': receiverId,
          'content': content,
          'messageType': messageType,
        }),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return Message.fromJson(data);
      } else {
        throw Exception('Erreur lors de l\'envoi du message: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur ChatService.sendMessage: $e');
      throw Exception('Erreur de connexion: $e');
    }
  }

  // Rechercher des utilisateurs
  static Future<List<UserSearchResult>> searchUsers(String query, {String? role}) async {
    try {
      final headers = await _getAuthHeaders();
      String url = '$baseUrl/users/search?query=$query';
      if (role != null) {
        url += '&role=$role';
      }
      
      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => UserSearchResult.fromJson(json)).toList();
      } else {
        throw Exception('Erreur lors de la recherche: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur ChatService.searchUsers: $e');
      throw Exception('Erreur de connexion: $e');
    }
  }

  // Marquer une conversation comme lue
  static Future<void> markConversationAsRead(String conversationId) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.put(
        Uri.parse('$baseUrl/conversations/$conversationId/read'),
        headers: headers,
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode != 200) {
        throw Exception('Erreur lors du marquage comme lu: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur ChatService.markConversationAsRead: $e');
      throw Exception('Erreur de connexion: $e');
    }
  }

  // Obtenir le nombre de messages non lus
  static Future<int> getUnreadCount() async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('$baseUrl/messages/unread-count'),
        headers: headers,
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['unreadCount'] ?? 0;
      } else {
        throw Exception('Erreur lors de la récupération du compteur: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur ChatService.getUnreadCount: $e');
      return 0;
    }
  }
}

// Modèles de données
class Conversation {
  final String id;
  final List<UserInfo> participants;
  final Message? lastMessage;
  final DateTime lastMessageTime;
  final bool isActive;

  Conversation({
    required this.id,
    required this.participants,
    this.lastMessage,
    required this.lastMessageTime,
    required this.isActive,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['_id'],
      participants: (json['participants'] as List)
          .map((p) => UserInfo.fromJson(p))
          .toList(),
      lastMessage: json['lastMessage'] != null 
          ? Message.fromJson(json['lastMessage']) 
          : null,
      lastMessageTime: DateTime.parse(json['lastMessageTime']),
      isActive: json['isActive'] ?? true,
    );
  }
}

class Message {
  final String id;
  final String conversationId;
  final UserInfo sender;
  final UserInfo receiver;
  final String content;
  final String messageType;
  final bool isRead;
  final DateTime? readAt;
  final DateTime createdAt;

  Message({
    required this.id,
    required this.conversationId,
    required this.sender,
    required this.receiver,
    required this.content,
    required this.messageType,
    required this.isRead,
    this.readAt,
    required this.createdAt,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['_id'],
      conversationId: json['conversationId'],
      sender: UserInfo.fromJson(json['senderId']),
      receiver: UserInfo.fromJson(json['receiverId']),
      content: json['content'],
      messageType: json['messageType'] ?? 'text',
      isRead: json['isRead'] ?? false,
      readAt: json['readAt'] != null ? DateTime.parse(json['readAt']) : null,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

class UserInfo {
  final String id;
  final String nom;
  final String prenom;
  final String? role;
  final String? photoUrl;
  final String? ville;

  UserInfo({
    required this.id,
    required this.nom,
    required this.prenom,
    this.role,
    this.photoUrl,
    this.ville,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['_id'],
      nom: json['nom'],
      prenom: json['prenom'],
      role: json['role'],
      photoUrl: json['photoUrl'],
      ville: json['ville'],
    );
  }

  String get fullName => '$prenom $nom';
}

class UserSearchResult extends UserInfo {
  UserSearchResult({
    required String id,
    required String nom,
    required String prenom,
    String? role,
    String? photoUrl,
    String? ville,
  }) : super(
    id: id,
    nom: nom,
    prenom: prenom,
    role: role,
    photoUrl: photoUrl,
    ville: ville,
  );

  factory UserSearchResult.fromJson(Map<String, dynamic> json) {
    return UserSearchResult(
      id: json['_id'],
      nom: json['nom'],
      prenom: json['prenom'],
      role: json['role'],
      photoUrl: json['photoUrl'],
      ville: json['ville'],
    );
  }
}

class MessagesResponse {
  final List<Message> messages;
  final Pagination pagination;

  MessagesResponse({
    required this.messages,
    required this.pagination,
  });

  factory MessagesResponse.fromJson(Map<String, dynamic> json) {
    return MessagesResponse(
      messages: (json['messages'] as List)
          .map((m) => Message.fromJson(m))
          .toList(),
      pagination: Pagination.fromJson(json['pagination']),
    );
  }
}

class Pagination {
  final int page;
  final int limit;
  final bool hasMore;

  Pagination({
    required this.page,
    required this.limit,
    required this.hasMore,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      page: json['page'],
      limit: json['limit'],
      hasMore: json['hasMore'] ?? false,
    );
  }
}
