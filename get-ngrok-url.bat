@echo off
echo 🔍 Récupération de l'URL ngrok actuelle...
echo.

REM Créer un fichier temporaire pour stocker la réponse
https://9bf32ca0d7f6.ngrok-free.app

if %errorlevel% neq 0 (
    echo ❌ Impossible de se connecter à l'API ngrok
    echo 💡 Vérifiez que ngrok est démarré
    pause
    exit /b 1
)

echo ✅ Réponse ngrok récupérée
echo.

REM Afficher le contenu pour debug
echo 📋 Contenu de la réponse ngrok:
type ngrok_response.json
echo.
echo.

REM Extraire l'URL HTTPS
echo 🔍 Recherche de l'URL HTTPS...
findstr "https.*ngrok-free.app" ngrok_response.json

echo.
echo 💡 Instructions manuelles:
echo 1. Copiez l'URL HTTPS ci-dessus (celle qui commence par https://)
echo 2. Ouvrez frontend\lib\config\api_config.dart
echo 3. Remplacez l'ancienne URL par la nouvelle dans la ligne:
echo    static const String baseUrl = 'NOUVELLE_URL_ICI';
echo 4. <PERSON>uvegardez le fichier
echo 5. Redémarrez votre application Flutter

REM Nettoyer
del ngrok_response.json 2>nul

pause
