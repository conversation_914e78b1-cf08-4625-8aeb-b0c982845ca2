const express = require('express');
const mongoose = require('mongoose');
require('dotenv').config();
const path = require('path');
const fs = require('fs');
const fsPromises = fs.promises;
const cors = require('cors');
const userRoutes = require('./routes/userRoutes');
const authRoutes = require('./routes/authRoutes');
const vehicleRoutes = require('./routes/vehicleRoutes');
// const chatRoutes = require('./routes/chatRoutes'); // Temporairement commenté
const imageType = require('image-type');

const app = express();

// Middleware pour ngrok
app.use((req, res, next) => {
  res.setHeader('ngrok-skip-browser-warning', 'true');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, ngrok-skip-browser-warning');
  next();
});

// Middleware CORS
app.use(cors());
app.use(express.json());

// Servir les fichiers statiques avec détection de type MIME
const uploadDir = path.join(__dirname, 'uploads'); // Correction: utiliser 'uploads' en minuscule
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

app.use('/uploads', async (req, res, next) => {
  const filePath = path.join(uploadDir, req.path);

  try {
    // Vérifier si le fichier existe
    if (!fs.existsSync(filePath)) {
      console.error(`Fichier non trouvé: ${filePath}`);
      return res.status(404).json({ message: 'Fichier non trouvé' });
    }

    const fileBuffer = await fsPromises.readFile(filePath);

    // Ajouter des en-têtes pour ngrok
    res.setHeader('ngrok-skip-browser-warning', 'true');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, ngrok-skip-browser-warning');

    // Détection du type MIME avec gestion d'erreur améliorée
    let mimeType = 'application/octet-stream'; // Type par défaut

    try {
      const imgType = await imageType(fileBuffer);
      if (imgType && imgType.mime) {
        mimeType = imgType.mime;
      } else {
        // Fallback basé sur l'extension du fichier
        const ext = path.extname(filePath).toLowerCase();
        switch (ext) {
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg';
            break;
          case '.png':
            mimeType = 'image/png';
            break;
          case '.gif':
            mimeType = 'image/gif';
            break;
          case '.webp':
            mimeType = 'image/webp';
            break;
          default:
            console.warn(`Extension non reconnue: ${ext}, utilisation du type par défaut`);
        }
      }
    } catch (typeError) {
      console.warn(`Erreur lors de la détection du type MIME pour ${filePath}:`, typeError);
      // Utiliser le type basé sur l'extension comme fallback
      const ext = path.extname(filePath).toLowerCase();
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          mimeType = 'image/jpeg';
          break;
        case '.png':
          mimeType = 'image/png';
          break;
        case '.gif':
          mimeType = 'image/gif';
          break;
        case '.webp':
          mimeType = 'image/webp';
          break;
      }
    }

    console.log(`Serving file: ${filePath}, MIME type: ${mimeType}, Size: ${fileBuffer.length} bytes`);

    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', fileBuffer.length);
    res.setHeader('Cache-Control', 'public, max-age=31536000');
    res.send(fileBuffer);

  } catch (error) {
    console.error('Erreur lors du service du fichier:', error);
    res.status(500).json({ message: 'Erreur serveur lors de la lecture du fichier' });
  }
});

// Connexion MongoDB
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log("MongoDB connecté"))
  .catch(err => console.log(err));

// Endpoint de santé pour tester la connectivité
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Serveur fonctionnel',
    timestamp: new Date().toISOString(),
    endpoints: {
      login: '/api/auth/login',
      register: '/api/users/register'
    }
  });
});

app.use('/api/users', userRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/vehicles', vehicleRoutes);

// Routes de chat ajoutées directement pour éviter les problèmes path-to-regexp
const authMiddleware = require('./middlewares/authMiddleware');
try {
  const { Conversation, Message } = require('./models/Chat');
  const User = require('./models/User');

  // Route pour récupérer les conversations
  app.get('/api/chat/conversations', authMiddleware, async (req, res) => {
    try {
      const userId = req.user.id;
      const conversations = await Conversation.find({
        participants: userId,
        isActive: true
      })
      .populate({
        path: 'participants',
        select: 'nom prenom role photoUrl',
        match: { _id: { $ne: userId } }
      })
      .populate({
        path: 'lastMessage',
        select: 'content createdAt senderId messageType'
      })
      .sort({ lastMessageTime: -1 });

      const validConversations = conversations.filter(conv =>
        conv.participants && conv.participants.length > 0
      );

      res.json(validConversations);
    } catch (error) {
      console.error('Erreur conversations:', error);
      res.status(500).json({ message: 'Erreur serveur', error: error.message });
    }
  });

  // Route pour rechercher des utilisateurs
  app.get('/api/chat/users/search', authMiddleware, async (req, res) => {
    try {
      const { query, role } = req.query;
      const currentUserId = req.user.id;

      if (!query || query.length < 2) {
        return res.status(400).json({ message: 'Requête de recherche trop courte' });
      }

      const searchCriteria = {
        _id: { $ne: currentUserId },
        $or: [
          { nom: { $regex: query, $options: 'i' } },
          { prenom: { $regex: query, $options: 'i' } }
        ]
      };

      if (role && ['client', 'conducteur'].includes(role)) {
        searchCriteria.role = role;
      }

      const users = await User.find(searchCriteria)
        .select('nom prenom role photoUrl ville')
        .limit(20);

      res.json(users);
    } catch (error) {
      console.error('Erreur recherche:', error);
      res.status(500).json({ message: 'Erreur serveur', error: error.message });
    }
  });

  // Route pour envoyer un message
  app.post('/api/chat/messages', authMiddleware, async (req, res) => {
    try {
      const { receiverId, content, messageType = 'text' } = req.body;
      const senderId = req.user.id;

      if (!receiverId || !content) {
        return res.status(400).json({ message: 'Destinataire et contenu requis' });
      }

      const receiver = await User.findById(receiverId);
      if (!receiver) {
        return res.status(404).json({ message: 'Destinataire non trouvé' });
      }

      let conversation = await Conversation.findOne({
        participants: { $all: [senderId, receiverId] },
        isActive: true
      });

      if (!conversation) {
        conversation = new Conversation({
          participants: [senderId, receiverId]
        });
        await conversation.save();
      }

      const message = new Message({
        conversationId: conversation._id,
        senderId,
        receiverId,
        content,
        messageType
      });

      await message.save();
      await message.populate('senderId', 'nom prenom photoUrl');
      await message.populate('receiverId', 'nom prenom photoUrl');

      res.status(201).json(message);
    } catch (error) {
      console.error('Erreur envoi message:', error);
      res.status(500).json({ message: 'Erreur serveur', error: error.message });
    }
  });

  console.log('✅ Routes de chat ajoutées avec succès');
} catch (error) {
  console.error('❌ Erreur lors de l\'ajout des routes de chat:', error);
}

app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Erreur serveur inattendue' });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Serveur démarré sur le port ${PORT}`));