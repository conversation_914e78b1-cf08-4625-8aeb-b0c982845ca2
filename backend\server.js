const express = require('express');
const mongoose = require('mongoose');
require('dotenv').config();
const path = require('path');
const fs = require('fs');
const fsPromises = fs.promises;
const cors = require('cors');
const userRoutes = require('./routes/userRoutes');
const authRoutes = require('./routes/authRoutes');
const vehicleRoutes = require('./routes/vehicleRoutes');
const imageType = require('image-type');

const app = express();

// Middleware pour ngrok
app.use((req, res, next) => {
  res.setHeader('ngrok-skip-browser-warning', 'true');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, ngrok-skip-browser-warning');
  next();
});

// Middleware CORS
app.use(cors());
app.use(express.json());

// Servir les fichiers statiques avec détection de type MIME
const uploadDir = path.join(__dirname, 'uploads'); // Correction: utiliser 'uploads' en minuscule
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

app.use('/uploads', async (req, res, next) => {
  const filePath = path.join(uploadDir, req.path);

  try {
    // Vérifier si le fichier existe
    if (!fs.existsSync(filePath)) {
      console.error(`Fichier non trouvé: ${filePath}`);
      return res.status(404).json({ message: 'Fichier non trouvé' });
    }

    const fileBuffer = await fsPromises.readFile(filePath);

    // Ajouter des en-têtes pour ngrok
    res.setHeader('ngrok-skip-browser-warning', 'true');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, ngrok-skip-browser-warning');

    // Détection du type MIME avec gestion d'erreur améliorée
    let mimeType = 'application/octet-stream'; // Type par défaut

    try {
      const imgType = await imageType(fileBuffer);
      if (imgType && imgType.mime) {
        mimeType = imgType.mime;
      } else {
        // Fallback basé sur l'extension du fichier
        const ext = path.extname(filePath).toLowerCase();
        switch (ext) {
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg';
            break;
          case '.png':
            mimeType = 'image/png';
            break;
          case '.gif':
            mimeType = 'image/gif';
            break;
          case '.webp':
            mimeType = 'image/webp';
            break;
          default:
            console.warn(`Extension non reconnue: ${ext}, utilisation du type par défaut`);
        }
      }
    } catch (typeError) {
      console.warn(`Erreur lors de la détection du type MIME pour ${filePath}:`, typeError);
      // Utiliser le type basé sur l'extension comme fallback
      const ext = path.extname(filePath).toLowerCase();
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          mimeType = 'image/jpeg';
          break;
        case '.png':
          mimeType = 'image/png';
          break;
        case '.gif':
          mimeType = 'image/gif';
          break;
        case '.webp':
          mimeType = 'image/webp';
          break;
      }
    }

    console.log(`Serving file: ${filePath}, MIME type: ${mimeType}, Size: ${fileBuffer.length} bytes`);

    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', fileBuffer.length);
    res.setHeader('Cache-Control', 'public, max-age=31536000');
    res.send(fileBuffer);

  } catch (error) {
    console.error('Erreur lors du service du fichier:', error);
    res.status(500).json({ message: 'Erreur serveur lors de la lecture du fichier' });
  }
});

// Connexion MongoDB
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log("MongoDB connecté"))
  .catch(err => console.log(err));

// Endpoint de santé pour tester la connectivité
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Serveur fonctionnel',
    timestamp: new Date().toISOString(),
    endpoints: {
      login: '/api/auth/login',
      register: '/api/users/register'
    }
  });
});

app.use('/api/users', userRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/vehicles', vehicleRoutes);

app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Erreur serveur inattendue' });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Serveur démarré sur le port ${PORT}`));