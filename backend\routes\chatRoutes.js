const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const {
  getUserConversations,
  getConversationMessages,
  sendMessage,
  searchUsers,
  markConversationAsRead,
  getUnreadCount
} = require('../controllers/chatController');

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware);

// Routes pour les conversations
router.get('/conversations', getUserConversations);
router.get('/conversations/:conversationId/messages', getConversationMessages);
router.put('/conversations/:conversationId/read', markConversationAsRead);

// Routes pour les messages
router.post('/messages', sendMessage);
router.get('/messages/unread-count', getUnreadCount);

// Route pour rechercher des utilisateurs
router.get('/users/search', searchUsers);

module.exports = router;
